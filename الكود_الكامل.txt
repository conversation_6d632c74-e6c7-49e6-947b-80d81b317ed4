الكود الكامل لمشروع Windows Forms Application
=============================================

1. ملف Program.cs
================

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WPF1
{
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new Form1());
        }
    }
}

=====================================

2. ملف Form1.cs
===============

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WPF1
{
    public partial class Form1 : Form
    {
        private Label lvlHello;
        private Button btnSet;
        private Button btnCancel;
        private Button btnLogout;

        public Form1()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 
            // lvlHello
            // 
            this.lvlHello = new Label();
            this.lvlHello.AutoSize = true;
            this.lvlHello.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.lvlHello.Location = new Point(50, 30);
            this.lvlHello.Name = "lvlHello";
            this.lvlHello.Size = new Size(0, 20);
            this.lvlHello.TabIndex = 0;
            this.lvlHello.Text = "";
            
            // 
            // btnSet
            // 
            this.btnSet = new Button();
            this.btnSet.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnSet.Location = new Point(50, 80);
            this.btnSet.Name = "btnSet";
            this.btnSet.Size = new Size(75, 23);
            this.btnSet.TabIndex = 1;
            this.btnSet.Text = "Set";
            this.btnSet.UseVisualStyleBackColor = true;
            this.btnSet.Click += new EventHandler(this.btnSet_Click);
            
            // 
            // btnCancel
            // 
            this.btnCancel = new Button();
            this.btnCancel.Location = new Point(150, 80);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(75, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);
            
            // 
            // btnLogout
            // 
            this.btnLogout = new Button();
            this.btnLogout.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnLogout.Location = new Point(250, 80);
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.Size = new Size(75, 23);
            this.btnLogout.TabIndex = 3;
            this.btnLogout.Text = "Logout";
            this.btnLogout.UseVisualStyleBackColor = true;
            this.btnLogout.Click += new EventHandler(this.btnLogout_Click);
            
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(400, 300);
            this.Controls.Add(this.btnLogout);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSet);
            this.Controls.Add(this.lvlHello);
            this.Name = "Form1";
            this.Text = "Form1";
            this.Load += new EventHandler(this.Form1_Load);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            lvlHello.Text = "Welcome";
        }

        private void btnSet_Click(object sender, EventArgs e)
        {
            lvlHello.Text = "Hello World";
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            lvlHello.Text = "Cancelled";
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}

=====================================

3. ملف WPF1.csproj
==================

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

</Project>

=====================================

4. ملف global.json
==================

{
  "sdk": {
    "version": "8.0.411"
  }
}

=====================================

5. ملف omnisharp.json
=====================

{
  "useModernNet": true,
  "enableRoslynAnalyzers": true
}

=====================================

6. ملف .vscode/settings.json
============================

{
    "dotnet.server.useOmnisharp": false
}

=====================================

7. ملف .vscode/launch.json
==========================

{
    "version": "0.2.0",
    "configurations": [
        {
            "name": ".NET Core Launch (console)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/WPF1/bin/Debug/net8.0-windows/WPF1.dll",
            "args": [],
            "cwd": "${workspaceFolder}/WPF1",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}

=====================================

8. ملف .vscode/tasks.json
=========================

{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "command": "dotnet",
            "type": "process",
            "args": [
                "build",
                "${workspaceFolder}/WPF1/WPF1.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "publish",
            "command": "dotnet",
            "type": "process",
            "args": [
                "publish",
                "${workspaceFolder}/WPF1/WPF1.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "watch",
            "command": "dotnet",
            "type": "process",
            "args": [
                "watch",
                "run",
                "--project",
                "${workspaceFolder}/WPF1/WPF1.csproj"
            ],
            "problemMatcher": "$msCompile"
        }
    ]
}

=====================================

أوامر التجميع والتشغيل:
=======================

1. لبناء المشروع:
dotnet build

2. لتشغيل المشروع:
dotnet run

3. لإنشاء ملف exe:
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true

=====================================

هيكل المجلدات:
===============

WPF1/
├── Program.cs
├── Form1.cs
├── WPF1.csproj
├── global.json
├── omnisharp.json
├── .vscode/
│   ├── settings.json
│   ├── launch.json
│   └── tasks.json
└── bin/
    └── Release/
        └── net8.0-windows/
            └── win-x64/
                └── publish/
                    └── WPF1.exe

=====================================

ملاحظات مهمة:
==============

1. جميع الملفات يجب أن تكون في ترميز UTF-8
2. ملف Program.cs هو نقطة البداية للتطبيق
3. ملف Form1.cs يحتوي على واجهة المستخدم والمنطق
4. ملف WPF1.csproj يحدد إعدادات المشروع
5. ملفات .vscode خاصة بإعدادات Visual Studio Code
6. ملف global.json يحدد إصدار .NET SDK المطلوب
7. ملف omnisharp.json لحل مشاكل C# Language Server

=====================================
