الكود الكامل لمشروع Windows Forms Application
=============================================

## التحديثات الأخيرة:
- تم إضافة نموذج تسجيل الدخول (LoginForm) مع واجهة عربية كاملة
- تم تحسين تصميم النموذج بلون طوبي (SaddleBrown) واسم مناسب "نظام تسجيل الدخول - شركة الإنتاج"
- تم إصلاح مشكلة المصادقة وإضافة التحقق من المدخلات باستخدام .Trim()
- تم تحسين الأزرار بألوان مميزة (أخضر للدخول، أحمر للإعادة)
- تم إضافة رسائل خطأ واضحة باللغة العربية مع عرض بيانات الدخول الصحيحة
- تم إنشاء ملف exe محدث قابل للتشغيل في: WPF1/bin/Release/net8.0-windows/win-x64/publish/WPF1.exe
- تم حل مشاكل C# Language Server

1. ملف Program.cs
================

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WPF1
{
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new LoginForm());
        }
    }
}

=====================================

2. ملف Form1.cs
===============

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WPF1
{
    public partial class Form1 : Form
    {
        private Label lvlHello;
        private Button btnSet;
        private Button btnCancel;
        private Button btnLogout;

        public Form1()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 
            // lvlHello
            // 
            this.lvlHello = new Label();
            this.lvlHello.AutoSize = true;
            this.lvlHello.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.lvlHello.Location = new Point(50, 30);
            this.lvlHello.Name = "lvlHello";
            this.lvlHello.Size = new Size(0, 20);
            this.lvlHello.TabIndex = 0;
            this.lvlHello.Text = "";
            
            // 
            // btnSet
            // 
            this.btnSet = new Button();
            this.btnSet.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnSet.Location = new Point(50, 80);
            this.btnSet.Name = "btnSet";
            this.btnSet.Size = new Size(75, 23);
            this.btnSet.TabIndex = 1;
            this.btnSet.Text = "Set";
            this.btnSet.UseVisualStyleBackColor = true;
            this.btnSet.Click += new EventHandler(this.btnSet_Click);
            
            // 
            // btnCancel
            // 
            this.btnCancel = new Button();
            this.btnCancel.Location = new Point(150, 80);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(75, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);
            
            // 
            // btnLogout
            // 
            this.btnLogout = new Button();
            this.btnLogout.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnLogout.Location = new Point(250, 80);
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.Size = new Size(75, 23);
            this.btnLogout.TabIndex = 3;
            this.btnLogout.Text = "Logout";
            this.btnLogout.UseVisualStyleBackColor = true;
            this.btnLogout.Click += new EventHandler(this.btnLogout_Click);
            
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(400, 300);
            this.Controls.Add(this.btnLogout);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSet);
            this.Controls.Add(this.lvlHello);
            this.Name = "Form1";
            this.Text = "Form1";
            this.Load += new EventHandler(this.Form1_Load);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            lvlHello.Text = "Welcome";
        }

        private void btnSet_Click(object sender, EventArgs e)
        {
            lvlHello.Text = "Hello World";
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            lvlHello.Text = "Cancelled";
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            // إنشاء نموذج تسجيل دخول جديد
            LoginForm loginForm = new LoginForm();
            loginForm.Show();

            // إغلاق النموذج الحالي
            this.Close();
        }
    }
}

=====================================

3. ملف WPF1.csproj
==================

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

</Project>

=====================================

4. ملف global.json
==================

{
  "sdk": {
    "version": "8.0.411"
  }
}

=====================================

5. ملف omnisharp.json
=====================

{
  "useModernNet": true,
  "enableRoslynAnalyzers": true
}

=====================================

6. ملف .vscode/settings.json
============================

{
    "dotnet.server.useOmnisharp": false
}

=====================================

7. ملف .vscode/launch.json
==========================

{
    "version": "0.2.0",
    "configurations": [
        {
            "name": ".NET Core Launch (console)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/WPF1/bin/Debug/net8.0-windows/WPF1.dll",
            "args": [],
            "cwd": "${workspaceFolder}/WPF1",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}

=====================================

8. ملف .vscode/tasks.json
=========================

{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "command": "dotnet",
            "type": "process",
            "args": [
                "build",
                "${workspaceFolder}/WPF1/WPF1.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "publish",
            "command": "dotnet",
            "type": "process",
            "args": [
                "publish",
                "${workspaceFolder}/WPF1/WPF1.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "problemMatcher": "$msCompile"
        },
        {
            "label": "watch",
            "command": "dotnet",
            "type": "process",
            "args": [
                "watch",
                "run",
                "--project",
                "${workspaceFolder}/WPF1/WPF1.csproj"
            ],
            "problemMatcher": "$msCompile"
        }
    ]
}

=====================================

#####################################
#                                   #
#     مشروع Login Form الجديد        #
#                                   #
#####################################

9. ملف LoginForm.cs (نموذج تسجيل الدخول)
=========================================

using System;
using System.Drawing;
using System.Windows.Forms;

namespace WPF1
{
    public partial class LoginForm : Form
    {
        private Label lblUserID;
        private Label lblPassword;
        private TextBox txtUserID;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnReset;

        public LoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            //
            // lblUserID
            //
            this.lblUserID = new Label();
            this.lblUserID.AutoSize = true;
            this.lblUserID.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Regular, GraphicsUnit.Point);
            this.lblUserID.Location = new Point(50, 50);
            this.lblUserID.Name = "lblUserID";
            this.lblUserID.Size = new Size(85, 26);
            this.lblUserID.TabIndex = 0;
            this.lblUserID.Text = "User ID";

            //
            // txtUserID
            //
            this.txtUserID = new TextBox();
            this.txtUserID.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.txtUserID.Location = new Point(200, 50);
            this.txtUserID.Name = "txtUserID";
            this.txtUserID.Size = new Size(200, 26);
            this.txtUserID.TabIndex = 1;

            //
            // lblPassword
            //
            this.lblPassword = new Label();
            this.lblPassword.AutoSize = true;
            this.lblPassword.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Regular, GraphicsUnit.Point);
            this.lblPassword.Location = new Point(50, 100);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new Size(106, 26);
            this.lblPassword.TabIndex = 2;
            this.lblPassword.Text = "Password";

            //
            // txtPassword
            //
            this.txtPassword = new TextBox();
            this.txtPassword.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.txtPassword.Location = new Point(200, 100);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.Size = new Size(200, 26);
            this.txtPassword.TabIndex = 3;
            this.txtPassword.UseSystemPasswordChar = true;
            this.txtPassword.KeyDown += new KeyEventHandler(this.txtPassword_KeyDown);

            //
            // btnLogin
            //
            this.btnLogin = new Button();
            this.btnLogin.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnLogin.Location = new Point(150, 160);
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.Size = new Size(100, 35);
            this.btnLogin.TabIndex = 4;
            this.btnLogin.Text = "Login";
            this.btnLogin.UseVisualStyleBackColor = true;
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);

            //
            // btnReset
            //
            this.btnReset = new Button();
            this.btnReset.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.btnReset.Location = new Point(270, 160);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new Size(100, 35);
            this.btnReset.TabIndex = 5;
            this.btnReset.Text = "Reset";
            this.btnReset.UseVisualStyleBackColor = true;
            this.btnReset.Click += new EventHandler(this.btnReset_Click);

            //
            // LoginForm
            //
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 250);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnLogin);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.lblPassword);
            this.Controls.Add(this.txtUserID);
            this.Controls.Add(this.lblUserID);
            this.Name = "LoginForm";
            this.Text = "Login Form";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.KeyPreview = true;
            this.KeyDown += new KeyEventHandler(this.LoginForm_KeyDown);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            // الحصول على البيانات المدخلة
            string ID = txtUserID.Text;
            string pass = txtPassword.Text;

            // التحقق من البيانات
            if (ID == "I25" && pass == "123456")
            {
                // إذا كانت البيانات صحيحة
                MessageBox.Show("Login Successful!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إنشاء وعرض النموذج الرئيسي
                Form1 mainForm = new Form1();
                mainForm.Show();

                // إخفاء نموذج تسجيل الدخول
                this.Hide();
            }
            else
            {
                // إذا كانت البيانات غير صحيحة
                MessageBox.Show("Password or User ID is incorrect!", "Login Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // مسح كلمة المرور
                txtPassword.Text = "";
                txtPassword.Focus();
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            // مسح محتوى الحقول
            txtUserID.Text = null;
            txtPassword.Text = null;

            // التركيز على حقل اسم المستخدم
            txtUserID.Focus();
        }

        // تسجيل الدخول بضغط Enter
        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }

        // إعادة التعيين بضغط Esc
        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnReset_Click(sender, e);
            }
        }
    }
}

=====================================

10. تحديث ملف Program.cs (لبدء التطبيق بنموذج تسجيل الدخول)
==========================================================

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WPF1
{
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            // بدء التطبيق بنموذج تسجيل الدخول بدلاً من Form1
            Application.Run(new LoginForm());
        }
    }
}

=====================================

11. تحديث ملف Form1.cs (إضافة وظيفة تسجيل الخروج)
===============================================

// إضافة هذه الدالة إلى Form1.cs الموجود

private void btnLogout_Click(object sender, EventArgs e)
{
    // إنشاء نموذج تسجيل دخول جديد
    LoginForm loginForm = new LoginForm();
    loginForm.Show();

    // إغلاق النموذج الحالي
    this.Close();
}

=====================================

مميزات Login Form الجديد:
=========================

1. **تصميم احترافي**:
   - حقل User ID مع Label بخط حجم 16
   - حقل Password مع إخفاء النص (UseSystemPasswordChar = true)
   - أزرار Login و Reset بتصميم واضح

2. **وظائف متقدمة**:
   - تسجيل الدخول بضغط Enter في حقل كلمة المرور
   - إعادة التعيين بضغط Esc في أي مكان في النموذج
   - رسائل تأكيد ونجاح وخطأ

3. **أمان أساسي**:
   - إخفاء كلمة المرور أثناء الكتابة
   - مسح كلمة المرور عند فشل تسجيل الدخول
   - التركيز التلقائي على الحقول المناسبة

4. **تجربة مستخدم محسنة**:
   - النموذج يظهر في وسط الشاشة
   - انتقال سلس بين النماذج
   - رسائل واضحة ومفهومة

=====================================

بيانات تسجيل الدخول الافتراضية:
===============================

User ID: I25
Password: 123456

=====================================

أوامر التجميع والتشغيل:
=======================

1. لبناء المشروع:
dotnet build

2. لتشغيل المشروع:
dotnet run

3. لإنشاء ملف exe:
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true

=====================================

هيكل المجلدات المحدث:
====================

WPF1/
├── Program.cs (محدث)
├── Form1.cs (محدث)
├── LoginForm.cs (جديد)
├── WPF1.csproj
├── global.json
├── omnisharp.json
├── .vscode/
│   ├── settings.json
│   ├── launch.json
│   └── tasks.json
└── bin/
    └── Release/
        └── net8.0-windows/
            └── win-x64/
                └── publish/
                    └── WPF1.exe

=====================================

ملاحظات مهمة:
==============

1. جميع الملفات يجب أن تكون في ترميز UTF-8
2. ملف Program.cs هو نقطة البداية للتطبيق (محدث لبدء بـ LoginForm)
3. ملف Form1.cs يحتوي على واجهة المستخدم والمنطق (محدث مع وظيفة Logout)
4. ملف LoginForm.cs الجديد يحتوي على نموذج تسجيل الدخول
5. ملف WPF1.csproj يحدد إعدادات المشروع
6. ملفات .vscode خاصة بإعدادات Visual Studio Code
7. ملف global.json يحدد إصدار .NET SDK المطلوب
8. ملف omnisharp.json لحل مشاكل C# Language Server

=====================================

معلومات تسجيل الدخول:
======================

- اسم المستخدم: I25
- كلمة المرور: 123456
- يمكن استخدام Enter لتسجيل الدخول
- يمكن استخدام Esc لإعادة التعيين
- زر Logout في النموذج الرئيسي يعيدك لشاشة تسجيل الدخول

=====================================

ملف exe التطبيق:
=================

تم إنشاء ملف exe للتطبيق بنجاح في المسار:
WPF1/bin/Release/net8.0-windows/win-x64/publish/WPF1.exe

مميزات الملف:
- يبدأ بشاشة تسجيل الدخول
- يحتوي على النموذج الرئيسي مع الأزرار الثلاثة
- وظيفة تسجيل الخروج تعود لشاشة تسجيل الدخول
- جميع المكتبات مدمجة في ملف واحد
- يعمل على أي جهاز Windows بدون تثبيت .NET

=====================================
