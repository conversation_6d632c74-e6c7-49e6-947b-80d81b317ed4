{"dotnet.server.useOmnisharp": false, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.codeLens.enableReferencesCodeLens": true, "dotnet.inlayHints.enableInlayHintsForParameters": true, "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true, "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true, "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true, "dotnet.inlayHints.enableInlayHintsForOtherParameters": true, "dotnet.inlayHints.enableInlayHintsForTypes": true, "dotnet.inlayHints.enableInlayHintsForImplicitVariableTypes": true, "dotnet.inlayHints.enableInlayHintsForLambdaParameterTypes": true, "dotnet.inlayHints.enableInlayHintsForImplicitObjectCreation": true, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableImportCompletion": true, "omnisharp.enableRoslynAnalyzers": true, "omnisharp.organizeImportsOnFormat": true, "omnisharp.useModernNet": true, "files.exclude": {"**/bin": true, "**/obj": true}}