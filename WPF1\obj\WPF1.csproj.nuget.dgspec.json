{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\c#------2\\WPF1\\WPF1.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\c#------2\\WPF1\\WPF1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\c#------2\\WPF1\\WPF1.csproj", "projectName": "WPF1", "projectPath": "C:\\Users\\<USER>\\Documents\\c#------2\\WPF1\\WPF1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\c#------2\\WPF1\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}