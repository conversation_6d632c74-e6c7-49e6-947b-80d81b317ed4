using System;
using System.Drawing;
using System.Windows.Forms;

namespace WPF1
{
    public partial class LoginForm : Form
    {
        private Label lblUserID;
        private Label lblPassword;
        private TextBox txtUserID;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnReset;

        public LoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // lblUserID
            // 
            this.lblUserID = new Label();
            this.lblUserID.AutoSize = true;
            this.lblUserID.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Regular, GraphicsUnit.Point);
            this.lblUserID.Location = new Point(50, 50);
            this.lblUserID.Name = "lblUserID";
            this.lblUserID.Size = new Size(85, 26);
            this.lblUserID.TabIndex = 0;
            this.lblUserID.Text = "User ID";

            // 
            // txtUserID
            // 
            this.txtUserID = new TextBox();
            this.txtUserID.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.txtUserID.Location = new Point(200, 50);
            this.txtUserID.Name = "txtUserID";
            this.txtUserID.Size = new Size(200, 26);
            this.txtUserID.TabIndex = 1;

            // 
            // lblPassword
            // 
            this.lblPassword = new Label();
            this.lblPassword.AutoSize = true;
            this.lblPassword.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Regular, GraphicsUnit.Point);
            this.lblPassword.Location = new Point(50, 100);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new Size(106, 26);
            this.lblPassword.TabIndex = 2;
            this.lblPassword.Text = "Password";

            // 
            // txtPassword
            // 
            this.txtPassword = new TextBox();
            this.txtPassword.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.txtPassword.Location = new Point(200, 100);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.Size = new Size(200, 26);
            this.txtPassword.TabIndex = 3;
            this.txtPassword.UseSystemPasswordChar = true;
            this.txtPassword.KeyDown += new KeyEventHandler(this.txtPassword_KeyDown);

            // 
            // btnLogin
            // 
            this.btnLogin = new Button();
            this.btnLogin.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnLogin.Location = new Point(150, 160);
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.Size = new Size(100, 35);
            this.btnLogin.TabIndex = 4;
            this.btnLogin.Text = "Login";
            this.btnLogin.UseVisualStyleBackColor = true;
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);

            // 
            // btnReset
            // 
            this.btnReset = new Button();
            this.btnReset.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Regular, GraphicsUnit.Point);
            this.btnReset.Location = new Point(270, 160);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new Size(100, 35);
            this.btnReset.TabIndex = 5;
            this.btnReset.Text = "Reset";
            this.btnReset.UseVisualStyleBackColor = true;
            this.btnReset.Click += new EventHandler(this.btnReset_Click);

            // 
            // LoginForm
            // 
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 250);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnLogin);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.lblPassword);
            this.Controls.Add(this.txtUserID);
            this.Controls.Add(this.lblUserID);
            this.Name = "LoginForm";
            this.Text = "Login Form";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.KeyPreview = true;
            this.KeyDown += new KeyEventHandler(this.LoginForm_KeyDown);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            // الحصول على البيانات المدخلة
            string ID = txtUserID.Text;
            string pass = txtPassword.Text;

            // التحقق من البيانات
            if (ID == "I25" && pass == "123456")
            {
                // إذا كانت البيانات صحيحة
                MessageBox.Show("Login Successful!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // إنشاء وعرض النموذج الرئيسي
                Form1 mainForm = new Form1();
                mainForm.Show();
                
                // إخفاء نموذج تسجيل الدخول
                this.Hide();
            }
            else
            {
                // إذا كانت البيانات غير صحيحة
                MessageBox.Show("Password or User ID is incorrect!", "Login Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                // مسح كلمة المرور
                txtPassword.Text = "";
                txtPassword.Focus();
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            // مسح محتوى الحقول
            txtUserID.Text = null;
            txtPassword.Text = null;
            
            // التركيز على حقل اسم المستخدم
            txtUserID.Focus();
        }

        // تسجيل الدخول بضغط Enter
        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }

        // إعادة التعيين بضغط Esc
        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnReset_Click(sender, e);
            }
        }
    }
}
