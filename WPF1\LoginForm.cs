using System;
using System.Drawing;
using System.Windows.Forms;

namespace WPF1
{
    public partial class LoginForm : Form
    {
        private Label lblUserID;
        private Label lblPassword;
        private TextBox txtUserID;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnReset;

        public LoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            //
            // lblUserID
            //
            this.lblUserID = new Label();
            this.lblUserID.AutoSize = true;
            this.lblUserID.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Bold, GraphicsUnit.Point);
            this.lblUserID.ForeColor = Color.White;
            this.lblUserID.Location = new Point(50, 80);
            this.lblUserID.Name = "lblUserID";
            this.lblUserID.Size = new Size(120, 26);
            this.lblUserID.TabIndex = 0;
            this.lblUserID.Text = "اسم المستخدم:";

            //
            // txtUserID
            //
            this.txtUserID = new TextBox();
            this.txtUserID.Font = new Font("Microsoft Sans Serif", 14F, FontStyle.Regular, GraphicsUnit.Point);
            this.txtUserID.Location = new Point(200, 80);
            this.txtUserID.Name = "txtUserID";
            this.txtUserID.Size = new Size(220, 29);
            this.txtUserID.TabIndex = 1;
            this.txtUserID.TextAlign = HorizontalAlignment.Center;

            //
            // lblPassword
            //
            this.lblPassword = new Label();
            this.lblPassword.AutoSize = true;
            this.lblPassword.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Bold, GraphicsUnit.Point);
            this.lblPassword.ForeColor = Color.White;
            this.lblPassword.Location = new Point(50, 140);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new Size(120, 26);
            this.lblPassword.TabIndex = 2;
            this.lblPassword.Text = "كلمة المرور:";

            //
            // txtPassword
            //
            this.txtPassword = new TextBox();
            this.txtPassword.Font = new Font("Microsoft Sans Serif", 14F, FontStyle.Regular, GraphicsUnit.Point);
            this.txtPassword.Location = new Point(200, 140);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.Size = new Size(220, 29);
            this.txtPassword.TabIndex = 2;
            this.txtPassword.UseSystemPasswordChar = true;
            this.txtPassword.TextAlign = HorizontalAlignment.Center;
            this.txtPassword.KeyDown += new KeyEventHandler(this.txtPassword_KeyDown);

            // 
            // btnLogin
            // 
            this.btnLogin = new Button();
            this.btnLogin.Font = new Font("Microsoft Sans Serif", 14F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnLogin.BackColor = Color.DarkGreen;
            this.btnLogin.ForeColor = Color.White;
            this.btnLogin.Location = new Point(200, 200);
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.Size = new Size(100, 40);
            this.btnLogin.TabIndex = 3;
            this.btnLogin.Text = "دخول";
            this.btnLogin.UseVisualStyleBackColor = false;
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);

            // 
            // btnReset
            // 
            this.btnReset = new Button();
            this.btnReset.Font = new Font("Microsoft Sans Serif", 14F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnReset.BackColor = Color.DarkRed;
            this.btnReset.ForeColor = Color.White;
            this.btnReset.Location = new Point(320, 200);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new Size(100, 40);
            this.btnReset.TabIndex = 4;
            this.btnReset.Text = "إعادة تعيين";
            this.btnReset.UseVisualStyleBackColor = false;
            this.btnReset.Click += new EventHandler(this.btnReset_Click);

            // 
            // LoginForm
            // 
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.SaddleBrown; // لون طوبي
            this.ClientSize = new Size(500, 280);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnLogin);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.lblPassword);
            this.Controls.Add(this.txtUserID);
            this.Controls.Add(this.lblUserID);
            this.Name = "LoginForm";
            this.Text = "نظام تسجيل الدخول - شركة الإنتاج";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.KeyPreview = true;
            this.KeyDown += new KeyEventHandler(this.LoginForm_KeyDown);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            // الحصول على البيانات المدخلة
            string ID = txtUserID.Text.Trim();
            string pass = txtPassword.Text.Trim();

            // التحقق من البيانات
            if (ID == "I25" && pass == "123456")
            {
                // إذا كانت البيانات صحيحة
                MessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إنشاء وعرض النموذج الرئيسي
                Form1 mainForm = new Form1();
                mainForm.Show();

                // إخفاء نموذج تسجيل الدخول
                this.Hide();

                // إغلاق نموذج تسجيل الدخول عند إغلاق النموذج الرئيسي
                mainForm.FormClosed += (s, args) => this.Close();
            }
            else
            {
                // إذا كانت البيانات غير صحيحة
                MessageBox.Show("بيانات تسجيل الدخول غير صحيحة!\n\nاسم المستخدم الصحيح: I25\nكلمة المرور الصحيحة: 123456",
                               "خطأ في تسجيل الدخول",
                               MessageBoxButtons.OK,
                               MessageBoxIcon.Error);

                // مسح الحقول والتركيز على اسم المستخدم
                txtUserID.Clear();
                txtPassword.Clear();
                txtUserID.Focus();
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            // مسح محتوى الحقول
            txtUserID.Clear();
            txtPassword.Clear();

            // التركيز على حقل اسم المستخدم
            txtUserID.Focus();
        }

        // تسجيل الدخول بضغط Enter
        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }

        // إعادة التعيين بضغط Esc
        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnReset_Click(sender, e);
            }
        }
    }
}
