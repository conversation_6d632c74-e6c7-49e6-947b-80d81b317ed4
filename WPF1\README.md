# تطبيق Windows Forms الأساسي

## الوصف
هذا تطبيق Windows Forms بسيط يوضح المفاهيم الأساسية لتطوير تطبيقات سطح المكتب باستخدام C# و .NET.

## المكونات
- **Label (lvlHello)**: يعرض النص الذي يتغير حسب الزر المضغوط
- **Button Set (btnSet)**: يغير النص إلى "Hello world again"
- **Button Cancel (btnCancel)**: يغير النص إلى "Text has been cancelled"
- **Button Logout (btnLogout)**: يغلق التطبيق

## كيفية التشغيل

### باستخدام سطر الأوامر:
```bash
cd WPF1
dotnet run
```

### باستخدام Visual Studio:
1. افتح ملف `WPF1.csproj` في Visual Studio
2. اضغط F5 أو Ctrl+F5 لتشغيل التطبيق

## الوظائف
- عند بدء التطبيق: يظهر النص "Hello World"
- عند النقر على "Set": يتغير النص إلى "Hello world again"
- عند النقر على "Cancel": يتغير النص إلى "Text has been cancelled"
- عند النقر على "Logout": يتم إغلاق التطبيق

## متطلبات النظام
- .NET 6.0 أو أحدث
- Windows 10/11
- Visual Studio 2022 (اختياري)

## بنية المشروع
```
WPF1/
├── WPF1.csproj          # ملف المشروع
├── Program.cs           # نقطة دخول التطبيق
├── Form1.cs             # النموذج الرئيسي مع المنطق
├── Form1.Designer.cs    # ملف التصميم
└── README.md           # هذا الملف
```
